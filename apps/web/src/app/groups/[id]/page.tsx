"use client";

import { useState, use } from "react";
import { useRouter } from "next/navigation";
import { trpc } from "@/utils/trpc";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Loader from "@/components/loader";
import { ArrowLeft, Edit, Trash2, ExternalLink, Calendar, TrendingUp } from "lucide-react";
import { toast } from "sonner";

interface GroupPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default function GroupPage({ params }: GroupPageProps) {
  const resolvedParams = use(params);
  const router = useRouter();
  const [isEditing, setIsEditing] = useState(false);
  const [editName, setEditName] = useState("");
  const [editDescription, setEditDescription] = useState("");

  const utils = trpc.useUtils();
  
  const { data: group, isLoading } = trpc.getGroup.useQuery({ id: resolvedParams.id });
  
  const updateGroupMutation = trpc.updateGroup.useMutation({
    onSuccess: () => {
      toast.success("Group updated successfully!");
      setIsEditing(false);
      utils.getGroup.invalidate({ id: resolvedParams.id });
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  const removeContentMutation = trpc.removeContentFromGroup.useMutation({
    onSuccess: () => {
      toast.success("Content removed from group!");
      utils.getGroup.invalidate({ id: resolvedParams.id });
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  const handleUpdateGroup = () => {
    if (!editName.trim()) {
      toast.error("Group name is required");
      return;
    }

    updateGroupMutation.mutate({
      id: resolvedParams.id,
      name: editName.trim(),
      description: editDescription.trim() || undefined,
    });
  };

  const handleRemoveContent = (contentId: number, contentTitle: string) => {
    if (confirm(`Remove "${contentTitle || 'this content'}" from the group?`)) {
      removeContentMutation.mutate({
        group_id: resolvedParams.id,
        content_id: contentId,
      });
    }
  };

  const startEditing = () => {
    if (group) {
      setEditName(group.name);
      setEditDescription(group.description || "");
      setIsEditing(true);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader />
      </div>
    );
  }

  if (!group) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
            Group not found
          </h1>
          <Button onClick={() => router.push("/groups")}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Groups
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex items-center gap-4 mb-8">
        <Button
          variant="ghost"
          onClick={() => router.push("/groups")}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Groups
        </Button>
      </div>

      {/* Group info */}
      <div className="mb-8">
        {isEditing ? (
          <Card>
            <CardHeader>
              <CardTitle>Edit Group</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="edit-name">Group Name</Label>
                <Input
                  id="edit-name"
                  value={editName}
                  onChange={(e) => setEditName(e.target.value)}
                  maxLength={100}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="edit-description">Description</Label>
                <Input
                  id="edit-description"
                  value={editDescription}
                  onChange={(e) => setEditDescription(e.target.value)}
                  maxLength={500}
                  placeholder="Optional description"
                />
              </div>

              <div className="flex gap-2">
                <Button
                  onClick={handleUpdateGroup}
                  disabled={updateGroupMutation.isPending}
                >
                  {updateGroupMutation.isPending ? "Saving..." : "Save Changes"}
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setIsEditing(false)}
                >
                  Cancel
                </Button>
              </div>
            </CardContent>
          </Card>
        ) : (
          <div className="flex items-start justify-between">
            <div>
              <h1 className="text-3xl font-semibold text-gray-900 dark:text-white">
                {group.name}
              </h1>
              {group.description && (
                <p className="text-gray-600 dark:text-gray-400 mt-2">
                  {group.description}
                </p>
              )}
              <div className="flex items-center gap-4 mt-4 text-sm text-gray-500">
                <span>{group.content_count} items</span>
                <span>Created {new Date(group.created_at).toLocaleDateString()}</span>
              </div>
            </div>
            
            <Button
              variant="outline"
              onClick={startEditing}
              className="flex items-center gap-2"
            >
              <Edit className="h-4 w-4" />
              Edit Group
            </Button>
          </div>
        )}
      </div>

      {/* Content grid */}
      {group.content && group.content.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {group.content.map((content) => (
            <Card key={content.id} className="hover:shadow-lg transition-shadow">
              <CardHeader className="flex flex-row items-start justify-between space-y-0">
                <div className="flex-1">
                  <CardTitle className="text-lg line-clamp-2">
                    {content.content_title || content.content_account}
                  </CardTitle>
                  {content.content_description && (
                    <CardDescription className="mt-2 line-clamp-3">
                      {content.content_description}
                    </CardDescription>
                  )}
                </div>
                
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleRemoveContent(content.id, content.content_title || "")}
                  className="text-red-500 hover:text-red-700 hover:bg-red-50"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </CardHeader>
              
              <CardContent>
                <div className="space-y-3">
                  {/* Content metadata */}
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400">
                      @{content.content_account}
                    </span>
                    <div className="flex items-center gap-1 text-gray-500">
                      <Calendar className="h-3 w-3" />
                      {new Date(content.content_created_date).toLocaleDateString()}
                    </div>
                  </div>

                  {/* Twitter metrics */}
                  {content.twitter_impressions > 0 && (
                    <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400">
                      <div className="flex items-center gap-1">
                        <TrendingUp className="h-3 w-3" />
                        {content.twitter_impressions.toLocaleString()} views
                      </div>
                      {content.twitter_likes > 0 && (
                        <span>♥ {content.twitter_likes}</span>
                      )}
                      {content.twitter_retweets > 0 && (
                        <span>🔄 {content.twitter_retweets}</span>
                      )}
                    </div>
                  )}

                  {/* Tags */}
                  {content.content_tags && content.content_tags.length > 0 && (
                    <div className="flex flex-wrap gap-1">
                      {content.content_tags.slice(0, 3).map((tag) => (
                        <span
                          key={tag}
                          className="px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 text-xs rounded"
                        >
                          {tag}
                        </span>
                      ))}
                      {content.content_tags.length > 3 && (
                        <span className="text-xs text-gray-500">
                          +{content.content_tags.length - 3} more
                        </span>
                      )}
                    </div>
                  )}

                  {/* View link */}
                  <div className="pt-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full"
                      onClick={() => window.open(content.content_link, "_blank")}
                    >
                      <ExternalLink className="h-3 w-3 mr-2" />
                      View Content
                    </Button>
                  </div>

                  {/* Added to group info */}
                  <div className="text-xs text-gray-500 border-t pt-2">
                    Added {new Date(content.added_at).toLocaleDateString()}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <Card className="text-center py-12">
          <CardContent>
            <div className="text-gray-400 mb-4">📁</div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              No content in this group yet
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              Start adding content by using the pin button on any content piece
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}